package com.decurret_dcp.dcjpy.bcmonitoring.exception;

/** Exception thrown when there is a WebSocket handshake error. */
public class WebSocketHandshakeException extends BcmonitoringException {

  private static final String ERROR_CODE = "WEBSOCKET_HANDSHAKE_ERROR";

  /**
   * Constructs a new WebSocketHandshakeException with the specified detail message.
   *
   * @param message the detail message
   */
  public WebSocketHandshakeException(String message) {
    super(ERROR_CODE, message);
  }

  /**
   * Constructs a new WebSocketHandshakeException with the specified detail message and cause.
   *
   * @param message the detail message
   * @param cause the cause of this exception
   */
  public WebSocketHandshakeException(String message, Throwable cause) {
    super(ERROR_CODE, message, cause);
  }
}
